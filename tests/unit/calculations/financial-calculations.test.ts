/**
 * Comprehensive Unit Tests for Core Financial Calculations
 * 
 * This test suite covers all core financial calculation functions with:
 * - Edge cases and boundary conditions
 * - Swiss-specific scenarios
 * - Precision validation
 * - Error handling
 */

import { describe, it, expect } from 'vitest';
import {
  calculateSavingsRate,
  calculateFIRENumber,
  calculateFIREProgress,
  calculateCompoundInterest,
  calculateYearsToFIRE,
  calculateNetWorth,
  calculateRealReturn,
  calculateRetirementIncome,
  calculatePillar3aTaxBenefit,
  calculateEffectiveTaxRate,
  validateFinancialInput,
  validateSwissInputs,
} from '../../../src/utils/financial-calculations';

describe('Core Financial Calculations', () => {
  describe('calculateSavingsRate', () => {
    it('should calculate savings rate correctly for positive values', () => {
      expect(calculateSavingsRate(10000, 7000)).toBeCloseTo(0.3, 5); // 30%
      expect(calculateSavingsRate(8500, 6000)).toBeCloseTo(0.294, 3); // ~29.4%
      expect(calculateSavingsRate(5000, 4000)).toBeCloseTo(0.2, 5); // 20%
    });

    it('should handle zero and negative expenses', () => {
      expect(calculateSavingsRate(10000, 0)).toBe(1.0); // 100% savings rate
      expect(calculateSavingsRate(10000, -1000)).toBeCloseTo(1.1, 5); // 110% (income + negative expenses)
    });

    it('should handle zero income', () => {
      expect(calculateSavingsRate(0, 5000)).toBe(0);
      expect(calculateSavingsRate(0, 0)).toBe(0);
    });

    it('should handle expenses exceeding income', () => {
      expect(calculateSavingsRate(5000, 8000)).toBeCloseTo(-0.6, 5); // -60% (deficit)
    });

    it('should handle very large numbers with precision', () => {
      expect(calculateSavingsRate(1000000, 750000)).toBeCloseTo(0.25, 5);
    });
  });

  describe('calculateFIRENumber', () => {
    it('should calculate basic FIRE number using 4% rule', () => {
      expect(calculateFIRENumber(60000)).toBeCloseTo(1875000, 2); // 60k * 25 * 1.25 safety margin
      expect(calculateFIRENumber(40000)).toBeCloseTo(1250000, 2); // 40k * 25 * 1.25
      expect(calculateFIRENumber(80000)).toBeCloseTo(2500000, 2); // 80k * 25 * 1.25
    });

    it('should account for continuing income', () => {
      // With 20k continuing income, only need to cover 40k from portfolio
      expect(calculateFIRENumber(60000, 20000)).toBeCloseTo(1250000, 2); // (60k - 20k) * 25 * 1.25
      expect(calculateFIRENumber(50000, 30000)).toBeCloseTo(625000, 2); // (50k - 30k) * 25 * 1.25
    });

    it('should handle continuing income exceeding expenses', () => {
      // If continuing income covers all expenses, FIRE number should be 0
      expect(calculateFIRENumber(50000, 60000)).toBe(0);
      expect(calculateFIRENumber(40000, 40000)).toBe(0);
    });

    it('should use custom withdrawal rate and safety margin', () => {
      // 3% withdrawal rate, no safety margin
      expect(calculateFIRENumber(60000, 0, 0.03, 1.0)).toBeCloseTo(2000000, 2); // 60k / 0.03
      // 5% withdrawal rate, 1.5x safety margin
      expect(calculateFIRENumber(60000, 0, 0.05, 1.5)).toBeCloseTo(1800000, 2); // 60k / 0.05 * 1.5
    });

    it('should handle zero expenses', () => {
      expect(calculateFIRENumber(0)).toBe(0);
    });
  });

  describe('calculateFIREProgress', () => {
    it('should calculate progress correctly', () => {
      expect(calculateFIREProgress(500000, 1500000)).toBeCloseTo(0.333, 3); // 33.3%
      expect(calculateFIREProgress(750000, 1500000)).toBeCloseTo(0.5, 5); // 50%
      expect(calculateFIREProgress(1200000, 1500000)).toBeCloseTo(0.8, 5); // 80%
    });

    it('should cap progress at 100%', () => {
      expect(calculateFIREProgress(1800000, 1500000)).toBe(1.0); // 100% max
      expect(calculateFIREProgress(2000000, 1500000)).toBe(1.0); // 100% max
    });

    it('should handle zero current wealth', () => {
      expect(calculateFIREProgress(0, 1500000)).toBe(0);
    });

    it('should handle zero FIRE goal', () => {
      expect(calculateFIREProgress(500000, 0)).toBe(0);
    });

    it('should handle negative values gracefully', () => {
      expect(calculateFIREProgress(-100000, 1500000)).toBeCloseTo(-0.067, 3);
    });
  });

  describe('calculateCompoundInterest', () => {
    it('should calculate compound interest with principal only', () => {
      // 10k at 5% for 10 years
      expect(calculateCompoundInterest(10000, 0.05, 10)).toBeCloseTo(16288.95, 2);
      // 50k at 7% for 20 years
      expect(calculateCompoundInterest(50000, 0.07, 20)).toBeCloseTo(193484.22, 2);
    });

    it('should calculate compound interest with monthly contributions', () => {
      // 10k principal + 1k monthly at 5% for 10 years
      expect(calculateCompoundInterest(10000, 0.05, 10, 1000)).toBeCloseTo(171465.88, 2);
      // 0 principal + 500 monthly at 6% for 15 years
      expect(calculateCompoundInterest(0, 0.06, 15, 500)).toBeCloseTo(146761.88, 2);
    });

    it('should handle zero interest rate', () => {
      expect(calculateCompoundInterest(10000, 0, 10, 1000)).toBe(130000); // 10k + (1k * 12 * 10)
      expect(calculateCompoundInterest(5000, 0, 5, 0)).toBe(5000); // No growth
    });

    it('should handle zero principal', () => {
      expect(calculateCompoundInterest(0, 0.05, 10, 1000)).toBeCloseTo(155176.93, 2);
    });

    it('should handle zero contributions', () => {
      expect(calculateCompoundInterest(10000, 0.05, 10, 0)).toBeCloseTo(16288.95, 2);
    });

    it('should handle very small interest rates', () => {
      expect(calculateCompoundInterest(10000, 0.001, 10, 100)).toBeCloseTo(22100.50, 2);
    });
  });

  describe('calculateYearsToFIRE', () => {
    it('should calculate years to FIRE correctly', () => {
      // Starting with 100k, saving 3k monthly, targeting 1.5M at 7% return
      const years = calculateYearsToFIRE(100000, 3000, 1500000, 0.07);
      expect(years).toBeGreaterThan(0);
      expect(years).toBeLessThan(30); // Should be reasonable
    });

    it('should return 0 if already at FIRE goal', () => {
      expect(calculateYearsToFIRE(1500000, 3000, 1500000, 0.07)).toBe(0);
      expect(calculateYearsToFIRE(2000000, 3000, 1500000, 0.07)).toBe(0);
    });

    it('should return Infinity for impossible scenarios', () => {
      expect(calculateYearsToFIRE(100000, 0, 1500000, 0.07)).toBe(Infinity); // No contributions
      expect(calculateYearsToFIRE(100000, -1000, 1500000, 0.07)).toBe(Infinity); // Negative contributions
    });

    it('should handle zero return rate', () => {
      const years = calculateYearsToFIRE(100000, 3000, 1500000, 0);
      expect(years).toBeCloseTo(38.89, 2); // (1.5M - 100k) / (3k * 12)
    });

    it('should handle small contributions relative to goal', () => {
      const years = calculateYearsToFIRE(0, 100, 1000000, 0.05);
      expect(years).toBeGreaterThan(50); // Should take a very long time
    });
  });

  describe('calculateNetWorth', () => {
    it('should calculate net worth correctly', () => {
      const assets = [500000, 100000, 50000]; // House, investments, cash
      const liabilities = [300000, 20000]; // Mortgage, car loan
      expect(calculateNetWorth(assets, liabilities)).toBe(330000);
    });

    it('should handle empty arrays', () => {
      expect(calculateNetWorth([], [])).toBe(0);
      expect(calculateNetWorth([100000], [])).toBe(100000);
      expect(calculateNetWorth([], [50000])).toBe(-50000);
    });

    it('should handle negative net worth', () => {
      const assets = [200000];
      const liabilities = [350000];
      expect(calculateNetWorth(assets, liabilities)).toBe(-150000);
    });

    it('should handle zero values', () => {
      expect(calculateNetWorth([0, 0], [0, 0])).toBe(0);
    });
  });

  describe('calculateRealReturn', () => {
    it('should calculate real return correctly', () => {
      expect(calculateRealReturn(0.07, 0.02)).toBeCloseTo(0.049, 3); // ~4.9%
      expect(calculateRealReturn(0.05, 0.03)).toBeCloseTo(0.0194, 4); // ~1.94%
      expect(calculateRealReturn(0.10, 0.04)).toBeCloseTo(0.0577, 4); // ~5.77%
    });

    it('should handle zero inflation', () => {
      expect(calculateRealReturn(0.07, 0)).toBe(0.07);
    });

    it('should handle zero nominal return', () => {
      expect(calculateRealReturn(0, 0.02)).toBeCloseTo(-0.0196, 4); // Negative real return
    });

    it('should handle high inflation scenarios', () => {
      expect(calculateRealReturn(0.05, 0.08)).toBeCloseTo(-0.0278, 4); // Negative real return
    });

    it('should handle equal nominal return and inflation', () => {
      expect(calculateRealReturn(0.03, 0.03)).toBeCloseTo(0, 5); // Zero real return
    });
  });

  describe('calculateRetirementIncome', () => {
    it('should calculate monthly retirement income correctly', () => {
      expect(calculateRetirementIncome(1500000)).toBeCloseTo(5000, 2); // 4% of 1.5M / 12
      expect(calculateRetirementIncome(1000000)).toBeCloseTo(3333.33, 2); // 4% of 1M / 12
      expect(calculateRetirementIncome(2000000)).toBeCloseTo(6666.67, 2); // 4% of 2M / 12
    });

    it('should handle custom withdrawal rates', () => {
      expect(calculateRetirementIncome(1500000, 0.03)).toBeCloseTo(3750, 2); // 3% withdrawal
      expect(calculateRetirementIncome(1500000, 0.05)).toBeCloseTo(6250, 2); // 5% withdrawal
    });

    it('should handle zero portfolio', () => {
      expect(calculateRetirementIncome(0)).toBe(0);
    });

    it('should handle zero withdrawal rate', () => {
      expect(calculateRetirementIncome(1500000, 0)).toBe(0);
    });
  });
});

describe('Swiss-Specific Calculations', () => {
  describe('calculatePillar3aTaxBenefit', () => {
    it('should calculate Pillar 3a tax benefit correctly', () => {
      // 7056 CHF contribution at 30% marginal tax rate
      expect(calculatePillar3aTaxBenefit(7056, 0.30)).toBeCloseTo(2116.8, 2);
      // 5000 CHF contribution at 25% marginal tax rate
      expect(calculatePillar3aTaxBenefit(5000, 0.25)).toBeCloseTo(1250, 2);
    });

    it('should handle zero contribution', () => {
      expect(calculatePillar3aTaxBenefit(0, 0.30)).toBe(0);
    });

    it('should handle zero tax rate', () => {
      expect(calculatePillar3aTaxBenefit(7056, 0)).toBe(0);
    });

    it('should handle maximum contribution limits', () => {
      // 2024 limit for employed persons
      expect(calculatePillar3aTaxBenefit(7056, 0.35)).toBeCloseTo(2469.6, 2);
    });
  });

  describe('calculateEffectiveTaxRate', () => {
    it('should calculate effective tax rate correctly', () => {
      const grossIncome = 100000;
      const federalTax = 5000;
      const cantonalTax = 8000;
      const municipalTax = 3000;
      const socialSecurity = 6000;
      
      const effectiveRate = calculateEffectiveTaxRate(
        grossIncome, federalTax, cantonalTax, municipalTax, socialSecurity
      );
      
      expect(effectiveRate).toBeCloseTo(0.22, 3); // 22% total tax burden
    });

    it('should handle zero income', () => {
      expect(calculateEffectiveTaxRate(0, 1000, 2000, 500, 1000)).toBe(0);
    });

    it('should handle zero taxes', () => {
      expect(calculateEffectiveTaxRate(100000, 0, 0, 0, 0)).toBe(0);
    });

    it('should handle high tax scenarios', () => {
      const effectiveRate = calculateEffectiveTaxRate(80000, 8000, 12000, 4000, 8000);
      expect(effectiveRate).toBeCloseTo(0.4, 3); // 40% tax burden
    });
  });
});

describe('Validation Functions', () => {
  describe('validateFinancialInput', () => {
    it('should validate correct inputs', () => {
      expect(validateFinancialInput(1000)).toEqual({ isValid: true });
      expect(validateFinancialInput(0)).toEqual({ isValid: true });
      expect(validateFinancialInput(999999)).toEqual({ isValid: true });
    });

    it('should reject invalid numbers', () => {
      expect(validateFinancialInput(NaN)).toEqual({ 
        isValid: false, 
        error: 'Value must be a valid number' 
      });
      expect(validateFinancialInput(Infinity)).toEqual({ 
        isValid: false, 
        error: 'Value must be a valid number' 
      });
    });

    it('should enforce minimum values', () => {
      expect(validateFinancialInput(-100, 0)).toEqual({ 
        isValid: false, 
        error: 'Value must be at least 0' 
      });
      expect(validateFinancialInput(50, 100)).toEqual({ 
        isValid: false, 
        error: 'Value must be at least 100' 
      });
    });

    it('should enforce maximum values', () => {
      expect(validateFinancialInput(1000, 0, 500)).toEqual({ 
        isValid: false, 
        error: 'Value must not exceed 500' 
      });
    });
  });

  describe('validateSwissInputs', () => {
    it('should validate correct Swiss inputs', () => {
      expect(validateSwissInputs('ZH', 30, 80000)).toEqual({ 
        isValid: true, 
        errors: [] 
      });
      expect(validateSwissInputs('GE', 45, 120000)).toEqual({ 
        isValid: true, 
        errors: [] 
      });
    });

    it('should reject invalid canton codes', () => {
      const result = validateSwissInputs('XX', 30, 80000);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid Swiss canton code');
    });

    it('should reject invalid ages', () => {
      const result1 = validateSwissInputs('ZH', 15, 80000);
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Age must be between 18 and 100');
      
      const result2 = validateSwissInputs('ZH', 105, 80000);
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Age must be between 18 and 100');
    });

    it('should reject invalid income values', () => {
      const result1 = validateSwissInputs('ZH', 30, -1000);
      expect(result1.isValid).toBe(false);
      expect(result1.errors).toContain('Income must be between 0 and 10,000,000 CHF');
      
      const result2 = validateSwissInputs('ZH', 30, 15000000);
      expect(result2.isValid).toBe(false);
      expect(result2.errors).toContain('Income must be between 0 and 10,000,000 CHF');
    });

    it('should accumulate multiple errors', () => {
      const result = validateSwissInputs('XX', 15, -1000);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3);
    });
  });
});
