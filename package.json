{"name": "swiss-budget-pro", "version": "1.0.0", "description": "Advanced Swiss financial planning and retirement calculator", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:e2e:install": "playwright install", "test:e2e:runner": "ts-node tests/e2e/scripts/run-tests.ts", "test:e2e:smoke": "npm run test:e2e:runner -- --suite smoke", "test:e2e:critical": "npm run test:e2e:runner -- --suite critical", "test:e2e:regression": "npm run test:e2e:runner -- --suite regression", "test:e2e:full": "npm run test:e2e:runner -- --suite full", "test:e2e:mobile": "npm run test:e2e:runner -- --suite ui --browsers chromium", "test:e2e:performance": "npm run test:e2e:runner -- --suite performance", "test:e2e:accessibility": "playwright test tests/e2e/tests/accessibility/", "test:e2e:visual": "playwright test tests/e2e/tests/visual/", "test:e2e:forms": "playwright test tests/e2e/tests/form-validation/", "test:e2e:data": "playwright test tests/e2e/tests/data-management/", "test:e2e:errors": "playwright test tests/e2e/tests/error-handling/", "test:e2e:journeys": "playwright test tests/e2e/tests/user-journeys/", "test:e2e:dry-run": "npm run test:e2e:runner -- --dry-run", "test:all": "npm run test:run && npm run test:e2e:smoke"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "autoprefixer": "^10.4.21", "d3": "^7.9.0", "decimal.js": "^10.5.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "postcss": "^8.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "tailwindcss": "^4.1.7"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/d3": "^7.4.3", "@types/decimal.js": "^0.0.32", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-i18next": "^7.8.3", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.2.0", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "keywords": ["react", "typescript", "financial-planning", "retirement-calculator", "swiss-finance", "budgeting"], "author": "Swiss Budget Pro Team", "license": "MIT"}